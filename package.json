{"name": "live_streaming_admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.10.0", "@mui/icons-material": "^6.4.1", "@mui/x-charts": "^7.24.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.7", "@shadcn/ui": "^0.0.4", "@tanstack/react-query": "^5.80.10", "@tanstack/react-table": "^8.20.6", "@tiptap/extension-character-count": "^2.22.0", "@tiptap/extension-code-block-lowlight": "^2.22.0", "@tiptap/extension-color": "^2.14.0", "@tiptap/extension-dropcursor": "^2.22.0", "@tiptap/extension-focus": "^2.22.0", "@tiptap/extension-gapcursor": "^2.22.0", "@tiptap/extension-hard-break": "^2.22.0", "@tiptap/extension-highlight": "^2.22.0", "@tiptap/extension-horizontal-rule": "^2.22.0", "@tiptap/extension-image": "^2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-list-item": "^2.22.0", "@tiptap/extension-mention": "^2.22.0", "@tiptap/extension-placeholder": "^2.22.0", "@tiptap/extension-subscript": "^2.22.0", "@tiptap/extension-superscript": "^2.22.0", "@tiptap/extension-table": "^2.14.0", "@tiptap/extension-table-cell": "^2.14.0", "@tiptap/extension-table-header": "^2.14.0", "@tiptap/extension-table-row": "^2.14.0", "@tiptap/extension-task-item": "^2.22.0", "@tiptap/extension-task-list": "^2.22.0", "@tiptap/extension-text-align": "^2.22.0", "@tiptap/extension-text-style": "^2.14.0", "@tiptap/extension-typography": "^2.22.0", "@tiptap/extension-underline": "^2.22.0", "@tiptap/pm": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "@types/chart.js": "^2.9.41", "axios": "^1.10.0", "chart.js": "^4.4.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "highlight.js": "^11.11.1", "lowlight": "^3.3.0", "lucide-react": "^0.473.0", "quill-image-uploader": "^1.3.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-hook-form": "^7.57.0", "react-icons": "^5.4.0", "react-query": "^3.39.3", "react-quill": "^2.0.0", "react-router-dom": "^7.1.3", "react-toastify": "^11.0.3", "recharts": "^2.15.0", "shadcn-ui": "^0.9.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.55", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/node": "^22.10.7", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-redux": "^7.1.34", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}