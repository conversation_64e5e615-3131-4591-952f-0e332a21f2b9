import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '@/store/authStore';
import axios from 'axios';
import { authEndpoints } from '@/globalurl/baseurl';

// Custom hook for authentication
export const useAuth = () => {
  const {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    checkAuth,
    updateProfile,
    clearError,
    setLoading,
  } = useAuthStore();

  const queryClient = useQueryClient();

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: ({ email, password }: { email: string; password: string }) =>
      login(email, password),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['profile'] });
    },
  });

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: logout,
    onSuccess: () => {
      queryClient.clear();
    },
  });

  // Profile query
  const profileQuery = useQuery({
    queryKey: ['profile'],
    queryFn: async () => {
      if (!token) throw new Error('No token available');
      
      const response = await axios.get(authEndpoints.profile, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      
      if (response.data.success) {
        return response.data.data;
      }
      throw new Error('Failed to fetch profile');
    },
    enabled: !!token && isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });

  // Auth check query
  const authCheckQuery = useQuery({
    queryKey: ['auth-check'],
    queryFn: checkAuth,
    enabled: !isAuthenticated,
    retry: false,
    refetchOnWindowFocus: false,
  });

  return {
    // State
    user,
    token,
    isAuthenticated,
    isLoading: isLoading || loginMutation.isPending || logoutMutation.isPending,
    error: error || loginMutation.error?.message || logoutMutation.error?.message,

    // Actions
    login: loginMutation.mutate,
    logout: logoutMutation.mutate,
    updateProfile,
    clearError,
    setLoading,

    // Query states
    profileData: profileQuery.data,
    isProfileLoading: profileQuery.isLoading,
    profileError: profileQuery.error,
    refetchProfile: profileQuery.refetch,

    // Mutation states
    isLoginLoading: loginMutation.isPending,
    isLogoutLoading: logoutMutation.isPending,
    loginError: loginMutation.error,
    logoutError: logoutMutation.error,
  };
};

// Hook for getting user data
export const useUser = () => {
  const { user, isAuthenticated } = useAuthStore();
  return {
    user,
    isAuthenticated,
    fullName: user ? `${user.firstname} ${user.lastname}` : '',
    isAdmin: user?.role === 'admin',
    isVerified: user?.isVerified && user?.otpVerified,
    isActive: user?.isActive,
  };
};

// Hook for protected routes
export const useRequireAuth = () => {
  const { isAuthenticated, isLoading } = useAuthStore();
  
  return {
    isAuthenticated,
    isLoading,
    shouldRedirect: !isLoading && !isAuthenticated,
  };
};
