import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import axios from 'axios';
import { toast } from 'react-toastify';
import { authEndpoints } from '@/globalurl/baseurl';

// Admin User Interface
export interface AdminUser {
  _id: string;
  firstname: string;
  lastname: string;
  email: string;
  role: string;
  isVerified: boolean;
  otpVerified: boolean;
  isActive: boolean;
  lastLogin: string | null;
  createdAt: string;
  updatedAt: string;
}

// Auth Store Interface
interface AuthStore {
  // State
  user: AdminUser | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  updateProfile: (data: Partial<AdminUser>) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

// Create Zustand store with persistence
export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Login action
      login: async (email: string, password: string) => {
        try {
          set({ isLoading: true, error: null });

          const response = await axios.post(authEndpoints.login, {
            email,
            password,
          });

          if (response.data.success) {
            const { admin, token } = response.data.data;
            
            // Update store state
            set({
              user: admin,
              token,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });

            // Save to localStorage as backup
            localStorage.setItem('adminToken', token);
            localStorage.setItem('adminData', JSON.stringify(admin));

            toast.success('Login successful!');
          } else {
            throw new Error('Login failed');
          }
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || 'Login failed. Please try again.';
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });
          toast.error(errorMessage);
          throw error;
        }
      },

      // Logout action
      logout: async () => {
        try {
          const { token } = get();
          
          if (token) {
            // Call logout API
            await axios.post(authEndpoints.logout, {}, {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            });
          }
        } catch (error) {
          console.error('Logout API error:', error);
        } finally {
          // Clear state and localStorage
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
          
          localStorage.removeItem('adminToken');
          localStorage.removeItem('adminData');
          
          toast.success('Logged out successfully');
        }
      },

      // Check authentication
      checkAuth: async () => {
        try {
          set({ isLoading: true });
          
          const { token, user } = get();
          let authToken = token;
          let userData = user;

          // Fallback to localStorage if not in store
          if (!authToken) {
            authToken = localStorage.getItem('adminToken');
          }
          if (!userData) {
            const storedData = localStorage.getItem('adminData');
            userData = storedData ? JSON.parse(storedData) : null;
          }

          if (!authToken || !userData) {
            set({ isLoading: false, isAuthenticated: false });
            return;
          }

          // Verify token with backend
          const response = await axios.get(authEndpoints.profile, {
            headers: {
              Authorization: `Bearer ${authToken}`,
            },
          });

          if (response.data.success) {
            set({
              user: response.data.data,
              token: authToken,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
            
            // Update localStorage
            localStorage.setItem('adminToken', authToken);
            localStorage.setItem('adminData', JSON.stringify(response.data.data));
          } else {
            throw new Error('Token verification failed');
          }
        } catch (error) {
          // Clear invalid auth data
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
          
          localStorage.removeItem('adminToken');
          localStorage.removeItem('adminData');
        }
      },

      // Update profile
      updateProfile: (data: Partial<AdminUser>) => {
        const { user } = get();
        if (user) {
          const updatedUser = { ...user, ...data };
          set({ user: updatedUser });
          localStorage.setItem('adminData', JSON.stringify(updatedUser));
        }
      },

      // Clear error
      clearError: () => set({ error: null }),

      // Set loading
      setLoading: (loading: boolean) => set({ isLoading: loading }),
    }),
    {
      name: 'auth-storage', // localStorage key
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Axios interceptor setup
export const setupAxiosInterceptors = () => {
  // Request interceptor
  axios.interceptors.request.use(
    (config) => {
      const { token } = useAuthStore.getState();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor
  axios.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response?.status === 401) {
        const { logout } = useAuthStore.getState();
        logout();
        window.location.href = '/login';
      }
      return Promise.reject(error);
    }
  );
};
