import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import axios from 'axios';
import { toast } from 'react-toastify';
import { authEndpoints } from '@/globalurl/baseurl';

// Admin User Interface
export interface AdminUser {
  _id: string;
  firstname: string;
  lastname: string;
  email: string;
  role: string;
  isVerified: boolean;
  otpVerified: boolean;
  isActive: boolean;
  lastLogin: string | null;
  createdAt: string;
  updatedAt: string;
}

// Auth Store Interface
interface AuthStore {
  // State
  user: AdminUser | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  updateProfile: (data: Partial<AdminUser>) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

// Create Zustand store with persistence
export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Login action
      login: async (email: string, password: string) => {
        try {
          set({ isLoading: true, error: null });

          console.log('Attempting login...');
          const response = await axios.post(authEndpoints.login, {
            email,
            password,
          });

          if (response.data.success) {
            const { admin, token } = response.data.data;

            console.log('Login successful, setting auth state');

            // Save to localStorage first
            localStorage.setItem('adminToken', token);
            localStorage.setItem('adminData', JSON.stringify(admin));

            // Then update store state
            set({
              user: admin,
              token,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });

            toast.success('Login successful!');
            console.log('Auth state updated successfully');
          } else {
            throw new Error('Login failed');
          }
        } catch (error: any) {
          console.log('Login error:', error);
          const errorMessage = error.response?.data?.message || 'Login failed. Please try again.';
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });
          toast.error(errorMessage);
          throw error;
        }
      },

      // Logout action
      logout: async () => {
        try {
          const { token, isAuthenticated } = get();

          // Only proceed if user is actually authenticated
          if (!isAuthenticated) {
            return;
          }

          console.log('Logging out...');

          if (token) {
            // Call logout API
            try {
              await axios.post(authEndpoints.logout, {}, {
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              });
            } catch (apiError) {
              console.error('Logout API error:', apiError);
              // Continue with logout even if API fails
            }
          }
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          // Clear state and localStorage
          console.log('Clearing auth state');
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });

          localStorage.removeItem('adminToken');
          localStorage.removeItem('adminData');

          toast.success('Logged out successfully');
        }
      },

      // Check authentication
      checkAuth: async () => {
        try {
          const { isAuthenticated: currentAuth, isLoading: currentLoading } = get();

          // Don't check if already authenticated or currently loading
          if (currentAuth || currentLoading) {
            return;
          }

          set({ isLoading: true });

          const { token, user } = get();
          let authToken = token;
          let userData = user;

          // Fallback to localStorage if not in store
          if (!authToken) {
            authToken = localStorage.getItem('adminToken');
          }
          if (!userData) {
            const storedData = localStorage.getItem('adminData');
            userData = storedData ? JSON.parse(storedData) : null;
          }

          if (!authToken) {
            set({ isLoading: false, isAuthenticated: false });
            return;
          }

          // Check if token is expired before making API call
          try {
            const payload = JSON.parse(atob(authToken.split('.')[1]));
            const currentTime = Date.now() / 1000;
            if (payload.exp < currentTime) {
              throw new Error('Token expired');
            }
          } catch (tokenError) {
            console.log('Token expired or invalid');
            // Token is invalid or expired
            set({
              user: null,
              token: null,
              isAuthenticated: false,
              isLoading: false,
              error: null,
            });
            localStorage.removeItem('adminToken');
            localStorage.removeItem('adminData');
            return;
          }

          // If we have both token and userData from localStorage, use them directly
          if (userData && authToken) {
            console.log('Using stored auth data');
            set({
              user: userData,
              token: authToken,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
            return;
          }

          // Only verify with backend if we don't have user data but have token
          console.log('Verifying token with backend');
          const response = await axios.get(authEndpoints.profile, {
            headers: {
              Authorization: `Bearer ${authToken}`,
            },
          });

          if (response.data.success) {
            set({
              user: response.data.data,
              token: authToken,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });

            // Update localStorage
            localStorage.setItem('adminToken', authToken);
            localStorage.setItem('adminData', JSON.stringify(response.data.data));
          } else {
            throw new Error('Token verification failed');
          }
        } catch (error) {
          console.log('Auth check failed:', error);
          // Only clear auth data if it's a real auth failure, not network error
          if (error?.response?.status === 401) {
            set({
              user: null,
              token: null,
              isAuthenticated: false,
              isLoading: false,
              error: null,
            });
            localStorage.removeItem('adminToken');
            localStorage.removeItem('adminData');
          } else {
            // For network errors, just stop loading but keep auth state
            set({ isLoading: false });
          }
        }
      },

      // Update profile
      updateProfile: (data: Partial<AdminUser>) => {
        const { user } = get();
        if (user) {
          const updatedUser = { ...user, ...data };
          set({ user: updatedUser });
          localStorage.setItem('adminData', JSON.stringify(updatedUser));
        }
      },

      // Clear error
      clearError: () => set({ error: null }),

      // Set loading
      setLoading: (loading: boolean) => set({ isLoading: loading }),
    }),
    {
      name: 'auth-storage', // localStorage key
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Axios interceptor setup
export const setupAxiosInterceptors = () => {
  // Clear any existing interceptors first
  axios.interceptors.request.clear();
  axios.interceptors.response.clear();

  // Request interceptor
  axios.interceptors.request.use(
    (config) => {
      const { token } = useAuthStore.getState();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor - be very careful about auto-logout
  axios.interceptors.response.use(
    (response) => response,
    (error) => {
      // Only auto-logout on 401 for specific auth endpoints and avoid logout loops
      if (error.response?.status === 401) {
        const url = error.config?.url || '';
        const isAuthEndpoint = url.includes('/auth/login') || url.includes('/auth/profile');
        const { isAuthenticated } = useAuthStore.getState();

        // Only logout if it's an auth endpoint and user is currently authenticated
        if (isAuthEndpoint && isAuthenticated) {
          console.log('401 on auth endpoint, logging out');
          const { logout } = useAuthStore.getState();
          logout();
          // Don't redirect immediately, let the app handle it
          setTimeout(() => {
            if (window.location.pathname !== '/login') {
              window.location.href = '/login';
            }
          }, 100);
        }
      }
      return Promise.reject(error);
    }
  );
};
