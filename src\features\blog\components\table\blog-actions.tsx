"use client";

import { useState } from "react";
import { MoreHorizontal, Edit, Trash2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { BlogType } from "../../type/blogType";

interface BlogActionsProps {
  blog: BlogType;
  onEdit: (blog: BlogType) => void;
  onDelete: (blog: BlogType) => void;
}

export const BlogActions = ({ blog, onEdit, onDelete }: BlogActionsProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleEdit = () => {
    setIsOpen(false);
    onEdit(blog);
  };

  const handleDelete = () => {
    setIsOpen(false);
    onDelete(blog);
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="h-8 w-8 p-0 hover:bg-gray-100"
          aria-label="Open menu"
        >
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuItem
          onClick={handleEdit}
          className="cursor-pointer flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50"
        >
          <Edit className="h-4 w-4" />
          Edit Blog
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={handleDelete}
          className="cursor-pointer flex items-center gap-2 text-red-600 hover:text-red-800 hover:bg-red-50"
        >
          <Trash2 className="h-4 w-4" />
          Delete Blog
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
