import { ReactNode, useEffect } from 'react';
import { useAuthStore, setupAxiosInterceptors } from '@/store/authStore';

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const { checkAuth, isAuthenticated, token } = useAuthStore();

  useEffect(() => {
    // Setup axios interceptors
    setupAxiosInterceptors();
    
    // Check authentication on app start
    if (!isAuthenticated) {
      checkAuth();
    }
  }, [checkAuth, isAuthenticated]);

  return <>{children}</>;
};
