import { ReactNode, useEffect, useRef } from 'react';
import { useAuthStore, setupAxiosInterceptors } from '@/store/authStore';

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const { checkAuth, isAuthenticated, token } = useAuthStore();
  const hasInitialized = useRef(false);

  useEffect(() => {
    // Setup axios interceptors only once
    if (!hasInitialized.current) {
      setupAxiosInterceptors();
      hasInitialized.current = true;
    }
  }, []);

  useEffect(() => {
    // Check authentication on app start only if not authenticated and has token
    if (!isAuthenticated && (token || localStorage.getItem('adminToken'))) {
      checkAuth();
    }
  }, []); // Remove dependencies to prevent infinite loops

  return <>{children}</>;
};
