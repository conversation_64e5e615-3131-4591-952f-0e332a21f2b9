import { ReactNode, useEffect, useRef } from 'react';
import { useAuthStore, setupAxiosInterceptors } from '@/store/authStore';

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const { checkAuth, isAuthenticated } = useAuthStore();
  const hasInitialized = useRef(false);
  const hasCheckedAuth = useRef(false);

  useEffect(() => {
    // Setup axios interceptors only once
    if (!hasInitialized.current) {
      setupAxiosInterceptors();
      hasInitialized.current = true;
    }
  }, []);

  useEffect(() => {
    // Check authentication only once on app start
    if (!hasCheckedAuth.current && !isAuthenticated) {
      const storedToken = localStorage.getItem('adminToken');
      const storedUser = localStorage.getItem('adminData');

      // Only check auth if we have stored credentials
      if (storedToken && storedUser) {
        checkAuth();
      }
      hasCheckedAuth.current = true;
    }
  }, [isAuthenticated, checkAuth]);

  return <>{children}</>;
};
